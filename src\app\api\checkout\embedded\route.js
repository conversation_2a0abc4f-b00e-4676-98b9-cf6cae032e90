import { NextResponse } from 'next/server'
import { stripe } from '../../../actions/stripe'
import { createTransaction, createEscrowTransaction } from '@/services/transactionService'
import { getUserById } from '@/services/userService'

export async function POST(request) {
  try {
    const {
      userId,
      userEmail,
      amount = 1000,
      currency = 'usd',
      productName = 'Demo Product',
      productDescription,
      // Escrow-specific fields
      isEscrow = false,
      sellerId,
      sellerEmail,
      sellerStripeAccountId,
      orderId
    } = await request.json()

    // Validate required fields
    if (!userId || !userEmail) {
      return NextResponse.json({ error: 'User ID and email are required' }, { status: 400 })
    }

    // Validate escrow-specific fields if needed
    if (isEscrow && (!sellerId || !sellerEmail || !orderId)) {
      return NextResponse.json({ 
        error: 'Escrow payments require sellerId, sellerEmail, and orderId' 
      }, { status: 400 })
    }

    const origin = request.headers.get('origin') || 'http://localhost:3000'

    // Get user details
    let userDetails
    try {
      const userResult = await getUserById(userId)
      if (userResult.success) {
        userDetails = userResult.user
      }
    } catch (error) {
      console.warn('Could not fetch user details:', error)
    }

    // Create or get Stripe customer
    let customer
    try {
      const existingCustomers = await stripe.customers.list({
        email: userEmail,
        limit: 1,
      })

      if (existingCustomers.data.length > 0) {
        customer = existingCustomers.data[0]
      } else {
        customer = await stripe.customers.create({
          email: userEmail,
          name: userDetails?.profile_name || undefined,
          metadata: {
            userId: userId,
          },
        })
      }
    } catch (error) {
      console.error('Error creating/getting customer:', error)
      return NextResponse.json({
        error: 'Failed to create customer',
        details: error.message,
      }, { status: 500 })
    }

    // Create transaction record in Firebase
    let transactionResult

    if (isEscrow) {
      // Create escrow transaction
      transactionResult = await createEscrowTransaction({
        userId,
        userEmail,
        sellerId,
        sellerEmail,
        sellerStripeAccountId,
        orderId,
        amount,
        currency,
        productName,
        productDescription,
        stripeCustomerId: customer.id,
        metadata: {
          origin,
          userAgent: request.headers.get('user-agent'),
        },
      })
    } else {
      // Create regular transaction
      transactionResult = await createTransaction({
        userId,
        userEmail,
        stripeCustomerId: customer.id,
        amount,
        currency,
        status: 'pending',
        productName,
        productDescription,
        metadata: {
          origin,
          userAgent: request.headers.get('user-agent'),
        },
      })
    }

    if (!transactionResult.success) {
      return NextResponse.json({ error: 'Failed to create transaction record' }, { status: 500 })
    }

    // Create embedded checkout session
    const session = await stripe.checkout.sessions.create({
      ui_mode: 'embedded',
      mode: 'payment',
      payment_method_types: ['card'],
      customer: customer.id,
      line_items: [
        {
          price_data: {
            currency,
            product_data: {
              name: productName,
              description: productDescription,
            },
            unit_amount: amount,
          },
          quantity: 1,
        },
      ],
      return_url: `${origin}/checkout/return?session_id={CHECKOUT_SESSION_ID}&transaction_id=${transactionResult.transactionId}${isEscrow ? `&order_id=${orderId}` : ''}`,
      metadata: {
        userId,
        transactionId: transactionResult.transactionId || '',
        ...(isEscrow && {
          isEscrow: 'true',
          sellerId,
          orderId
        })
      },
    })

    // Update transaction with session ID
    if (transactionResult.transactionId) {
      const { updateTransaction } = await import('@/services/transactionService')
      await updateTransaction(transactionResult.transactionId, {
        stripeSessionId: session.id,
        metadata: {
          origin,
          userAgent: request.headers.get('user-agent'),
          sessionCreated: new Date().toISOString(),
        },
      })
    }

    return NextResponse.json({
      clientSecret: session.client_secret,
      sessionId: session.id,
      customerId: customer.id,
      transactionId: transactionResult.transactionId,
      ...(isEscrow && {
        isEscrow: true,
        orderId,
        sellerId
      })
    })
  } catch (error) {
    console.error('Embedded checkout error:', error)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}
